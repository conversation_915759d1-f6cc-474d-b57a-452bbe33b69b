﻿using System;
using System.Threading;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using uBuyFirst.BrowseAPI;
using uBuyFirst.License;
using uBuyFirst.Prefs;
using uBuyFirst.Properties;
using uBuyFirst.Search.FIA;
using uBuyFirst.Tools;

namespace uBuyFirst.Search
{
    public class SearchService
    {
        public static ConcurrentDictionary<string, HashSet<string>> Old = new();
        public static bool IdleTimeEnabled;
        public static decimal IdleTimeMaximum;
        private readonly FiaService _fiaService;
        private readonly Helpers.AdvancedSearchConfig _requestsCfg;

        private CancellationTokenSource _ctsStopSearch;
        private CancellationToken _cancellationToken;
        private readonly EndNowFinder _endNowFinder;
        public bool IsApiRunning { get; set; }
        public bool IsRssRunning { get; set; }
        public bool IsRss2Running { get; set; }
        public bool IsOofSRunning { get; set; }
        public bool IsWatchlistRunning { get; set; }
        public bool SearchMustBeRunning { get; set; }

        // Dual-phase search tracking properties
        public bool IsPriceFirstPhaseComplete { get; set; }
        public bool IsRegularPhaseComplete { get; set; }

        public bool Running { get; set; } //> IsApiRunning || IsRssRunning || IsRss2Running || _endNowFinder.IsEndNowRunning;
        private readonly LicenseUtility _licenseUtility;
        private readonly ViewReporter _viewReporter;
        private readonly BrowseAPINetwork? _browseApiService;

        const string IgnoredErrors = "Timeouts are not supported on this stream.;The operation has timed out.;The remote server returned an unexpected response: (502) Bad Gateway.";

        internal SearchService(Helpers.AdvancedSearchConfig requestsCfg, LicenseUtility licenseUtility, ViewReporter viewReporter, SynchronizationContext synchronizationContext, Action<FoundItem> handleNewItemAction)
        {
            _viewReporter = viewReporter;
            _licenseUtility = licenseUtility;

            _requestsCfg = requestsCfg;
            _fiaService = new FiaService(viewReporter);
            _browseApiService = new BrowseAPINetwork(viewReporter);
            _endNowFinder = new EndNowFinder(_fiaService, _browseApiService);
        }

        public async Task PerformInitialSearch(List<Keyword2Find> enabledKeywords)
        {
            SearchMustBeRunning = true;

            // Initialize phase tracking
            IsPriceFirstPhaseComplete = false;
            IsRegularPhaseComplete = false;

            // Check if dual-phase loading is enabled
            if (ConnectionConfig.LoadLowPriceFirst)
            {
                // Phase 1: Price-first search
                await PerformInitialSearchPriceFirst(enabledKeywords);

                // Phase 2: Regular search
                await PerformInitialSearchRegular(enabledKeywords);
                IsRegularPhaseComplete = true;
            }
            else
            {
                // Single-phase search (original behavior)
                await PerformInitialSearchRegular(enabledKeywords);
                IsRegularPhaseComplete = true;
                IsPriceFirstPhaseComplete = true; // Mark as complete for consistency
            }
        }

        private async Task PerformInitialSearchRegular(List<Keyword2Find> enabledKeywords)
        {
            await Task.Run(() =>
            {
                _ctsStopSearch = new CancellationTokenSource();
                _cancellationToken = _ctsStopSearch.Token;

                if (ConnectionConfig.FindingAPIEnabled)
                    _endNowFinder.PerformInitialSearch(enabledKeywords, _cancellationToken);

                if (ConnectionConfig.BrowseAPIEnabled)
                    _endNowFinder.PerformInitialSearchBrowseAPI(enabledKeywords, _cancellationToken);

                PerformInitialSearchApi(enabledKeywords);
            });
        }

        public async Task PerformInitialSearchPriceFirst(List<Keyword2Find> enabledKeywords)
        {
            IsPriceFirstPhaseComplete = false;
            await Task.Run(() =>
            {
                // Ensure cancellation token is available
                if (_ctsStopSearch == null)
                {
                    _ctsStopSearch = new CancellationTokenSource();
                    _cancellationToken = _ctsStopSearch.Token;
                }

                // Perform price-first searches for ending now items
                if (ConnectionConfig.FindingAPIEnabled)
                    _endNowFinder.PerformInitialSearch(enabledKeywords, _cancellationToken);

                if (ConnectionConfig.BrowseAPIEnabled)
                    _endNowFinder.PerformInitialSearchBrowseAPI(enabledKeywords, _cancellationToken);

                // Perform price-first searches for regular items
                PerformInitialSearchApiPriceFirst(enabledKeywords);
            });
            IsPriceFirstPhaseComplete = true;
        }

        private void PerformInitialSearchApiPriceFirst(List<Keyword2Find> enabledKeywords)
        {
            var eligibleKeywords = enabledKeywords.Where(kw => kw.ListingType.Contains(ListingType.BuyItNow) || kw.ListingType.Contains(ListingType.AuctionsStartedNow)).ToList();

            if (eligibleKeywords.Count == 0)
                return;

            try
            {
                for (var i = 0; i < eligibleKeywords.Count; i++)
                {
                    if (!SearchMustBeRunning)
                        return;

                    var kw = eligibleKeywords[i];

                    if (ConnectionConfig.BrowseAPIEnabled)
                    {
                        foreach (var categoryID in kw.Categories4Api.Split(','))
                        {
                            _browseApiService?.FindRequestsApi(_cancellationToken, kw, categoryID, false, true, BrowseAPISortOrder.Price);
                        }
                    }

                    if (ConnectionConfig.FindingAPIEnabled)
                    {
                        _fiaService.FindRequestsApi(_cancellationToken, kw, false, true);
                    }

                    _viewReporter.ReportDots(1);
                }
            }
            catch (Exception ex)
            {
                if (!IgnoredErrors.Contains(ex.Message))
                    ExM.ubuyExceptionHandler("BackgroundWorker: ", ex);
            }
        }

        private void PerformInitialSearchApi(List<Keyword2Find> enabledKeywords)
        {
            var eligibleKeywords = enabledKeywords.Where(kw => kw.ListingType.Contains(ListingType.BuyItNow) || kw.ListingType.Contains(ListingType.AuctionsStartedNow)).ToList();

            if (eligibleKeywords.Count == 0)
                return;

            IsApiRunning = true;
            try
            {
                for (var i = 0; i < eligibleKeywords.Count; i++)
                {
                    //if (_cancellationToken.IsCancellationRequested)
                    if (!SearchMustBeRunning)
                    {
                        IsApiRunning = false;

                        return;
                    }

                    var kw = eligibleKeywords[i];

                    if (ConnectionConfig.BrowseAPIEnabled)
                    {
                        foreach (var categoryID in kw.Categories4Api.Split(','))
                        {
                            _browseApiService?.FindRequestsApi(_cancellationToken, kw, categoryID, false, true);
                        }
                    }

                    if (ConnectionConfig.FindingAPIEnabled)
                    {
                        _fiaService.FindRequestsApi(_cancellationToken, kw, false, true);
                    }

                    _viewReporter.ReportDots(1);
                    //Debug.WriteLine("Api request");
                }
            }
            catch (Exception ex)
            {
                if (!IgnoredErrors.Contains(ex.Message))
                    ExM.ubuyExceptionHandler("BackgroundWorker: ", ex);
            }

            IsApiRunning = false;
        }

        public void SearchLoopV2(List<Keyword2Find> enabledKeywords)
        {
            if (ConnectionConfig.FindingAPIEnabled)
                _endNowFinder.StartSearchLoop(_cancellationToken, enabledKeywords);
            if (ConnectionConfig.BrowseAPIEnabled)
                _endNowFinder.StartSearchLoopBrowseAPI(_cancellationToken, enabledKeywords);
            SearchLoopApi(enabledKeywords);
            //SearchLoopRss(enabledKeywords);
            SearchLoopRss2(enabledKeywords);
            SearchLoopOutOfStock(enabledKeywords);
        }

        private async void SearchLoopApi(List<Keyword2Find> enabledKeywords)
        {
            //TODO:disabled until, check allowedInterval, check frequency, check cancellation
            if (!_requestsCfg.EnabledApi)
                return;

            var eligibleKeywords = enabledKeywords.Where(kw => kw.ListingType.Contains(ListingType.BuyItNow) || kw.ListingType.Contains(ListingType.AuctionsStartedNow)).ToList();

            if (eligibleKeywords.Count == 0)
                return;
            var findingAPITimeStamp = DateTime.UtcNow;
            await Task.Run(async () =>
            {
                IsApiRunning = true;
                while (SearchMustBeRunning)
                {
                    foreach (var kw in eligibleKeywords)
                    {
                        if (CheckIfUserIsIdle())
                            return;

                        var browseAPIAllowedInterval = (int)(_licenseUtility.CurrentLimits.SearchInterval * Config.BrowseSearchCoefficient * 1000);
                        await Task.Delay(browseAPIAllowedInterval, _cancellationToken).ContinueWith(_ => { });

                        if (_cancellationToken.IsCancellationRequested || !Form1.Instance.IsHandleCreated)
                            return;

                        if (!IsKeywordReadyForRequest(kw))
                            continue;
                        try
                        {
                            if (ConnectionConfig.BrowseAPIEnabled)
                            {
                                foreach (var categoryID in kw.Categories4Api.Split(','))
                                    _browseApiService?.FindRequestsApi(_cancellationToken, kw, categoryID, false, false);
                                _viewReporter.ReportDots(1);
                            }

                            var findingAPIAllowedInterval = (int)(_licenseUtility.CurrentLimits.SearchInterval * Config.FindingAPICoefficient * 1000);

                            if ((DateTime.UtcNow - findingAPITimeStamp).TotalMilliseconds > findingAPIAllowedInterval)
                            {
                                findingAPITimeStamp = DateTime.UtcNow;
                                if (ConnectionConfig.FindingAPIEnabled)
                                {
                                    if (FiaService.FindItemsAdvancedFails <= 0)
                                    {
                                        _fiaService.FindRequestsApi(_cancellationToken, kw, false, false);
                                        _viewReporter.ReportDots(1);
                                    }
                                    else
                                        FiaService.FindItemsAdvancedFails--;
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            if (!IgnoredErrors.Contains(ex.Message))
                                ExM.ubuyExceptionHandler("BackgroundWorker: ", ex);
                        }
                    }

                    await Task.Delay(100);
                }
            }, _cancellationToken).ContinueWith(_ => { IsApiRunning = false; });
        }

        public async void SearchLoopRss2(List<Keyword2Find> enabledKeywords)
        {
            //TODO:disabled until, check allowedInterval, check frequency, check cancellation
            if (!_requestsCfg.EnabledRss2 || Program.Sandbox)
                return;

            var eligibleKeywords = enabledKeywords.Where(kw => kw.ListingType.Contains(ListingType.BuyItNow) || kw.ListingType.Contains(ListingType.AuctionsStartedNow)).ToList();

            if (eligibleKeywords.Count == 0)
                return;

            await Task.Run(async () =>
            {
                IsRss2Running = true;
                //while (!_cancellationToken.IsCancellationRequested)
                while (SearchMustBeRunning)
                {
                    foreach (var kw in eligibleKeywords)
                    {
                        var allowedInterval = _licenseUtility.CurrentLimits.SearchInterval / eligibleKeywords.Count * 1000;
                        if (allowedInterval == 0)
                            allowedInterval = 1000;
                        await Task.Delay(allowedInterval, _cancellationToken).ContinueWith(_ => { });

                        if (_cancellationToken.IsCancellationRequested || !Form1.Instance.IsHandleCreated)
                            return;

                        if (!IsKeywordReadyForRequest(kw))
                            continue;
                        try
                        {
                            //FindRequestsRssV2(_cancellationToken, kw);
                            _viewReporter.ReportDots(1);
                        }
                        catch (Exception ex)
                        {
                            if (!IgnoredErrors.Contains(ex.Message))
                                ExM.ubuyExceptionHandler("BackgroundWorker: ", ex);
                        }
                    }
                }
            }, _cancellationToken).ContinueWith(_ =>
            {
                Debug.WriteLine("RSS2 Stopped");
                IsRss2Running = false;
            });
        }

        public async void SearchLoopOutOfStock(List<Keyword2Find> enabledKeywords)
        {
            //TODO:disabled until, check allowedInterval, check frequency, check cancellation
            if (Program.Sandbox)
                return;

            var eligibleKeywords = enabledKeywords.Where(kw => kw.ListingType.Contains(ListingType.OutOfStock)).ToList();

            if (eligibleKeywords.Count == 0)
                return;

            await Task.Run(async () =>
            {
                IsOofSRunning = true;
                //while (!_cancellationToken.IsCancellationRequested)
                while (SearchMustBeRunning)
                {
                    foreach (var kw in eligibleKeywords)
                    {
                        await ThrottleRequests(eligibleKeywords.Count);

                        if (_cancellationToken.IsCancellationRequested)
                            return;

                        if (!IsKeywordReadyForRequest(kw))
                            continue;

                        try
                        {
                            var itemIds = kw.Kws.Split(',').Select(k => k.Trim()).ToList();
                            //foreach (var itemId in itemIds)
                            // {
                            //    await OutOfStockSearcher.SearchWithBrowseAPI(new List<string>() { itemId }, kw, _viewReporter, _cancellationToken);
                            //    _viewReporter.ReportDots(1);
                            //    if (_cancellationToken.IsCancellationRequested)
                            //        return;
                            // }

                            var batches = itemIds.Batch(20);
                            foreach (var batch in batches)
                            {
                                var strings = batch.ToArray();
                                await OutOfStockSearcher.SearchWithBrowseAPI(strings, kw, _viewReporter, _cancellationToken);
                                _viewReporter.ReportDots(1);

                                if (_cancellationToken.IsCancellationRequested)
                                    return;
                            }
                        }
                        catch (Exception ex)
                        {
                            if (!IgnoredErrors.Contains(ex.Message))
                                ExM.ubuyExceptionHandler("BackgroundWorker: ", ex);
                        }
                    }

                    await Task.Delay(1 * 1000, _cancellationToken);
                }
            }, _cancellationToken).ContinueWith(_ => { IsOofSRunning = false; });
        }

        private bool IsKeywordReadyForRequest(Keyword2Find kw)
        {
            // Sanity check for clock changes or future dates. This can happen if the system clock is moved forward and then back.
            if (kw.LastRequested > DateTime.UtcNow)
                kw.LastRequested = DateTime.UtcNow;

            var timeSinceLastRequest = DateTime.UtcNow - kw.LastRequested;

            // Check if the elapsed time is less than the configured frequency.
            if (timeSinceLastRequest < kw.Frequency)
                return false;

            // Update the last requested time and signal that the request can proceed.
            kw.LastRequested = DateTime.UtcNow;
            return true;
        }

        private async Task ThrottleRequests(int keywordsCount)
        {
            var allowedIntervalMs = 1000; // _licenseUtility.CurrentLimits.SearchInterval / keywordsCount * 1000;
            if (allowedIntervalMs == 0)
                allowedIntervalMs = 100;
            await Task.Delay(allowedIntervalMs, _cancellationToken).ContinueWith(_ => { });
        }

        internal static void SendItemsForProcessing(List<FoundItem> addedItems, ViewReporter viewReporter)
        {
            foreach (var addedItem in addedItems)
            {
                viewReporter.ReportItem(addedItem);
            }
        }

        public bool CheckIfUserIsIdle()
        {
            if (IdleTimeEnabled)
            {
                var idleTime = Math.Round((decimal)ProgramState.Idlesw.Elapsed.TotalSeconds, 0);
                if (idleTime > IdleTimeMaximum * 60 + 30)
                {
                    var dialogResult = XtraMessageBox.Show(En_US.Form1MainStart3SearchingPausedDueToInactivityIdleTimeoutSettingContinue, @"Pause", MessageBoxButtons.YesNo, MessageBoxIcon.Question,
                        MessageBoxDefaultButton.Button1);
                    if (dialogResult == DialogResult.No)
                    {
                        Form1.Instance.InvokeIfRequired(() => _ = Form1.Instance.StopWorking());

                        //_ctsStopSearch.Cancel();
                        return true;
                    }

                    ProgramState.Idlesw.Restart();
                }
            }

            return false;
        }

        public async Task StopSearch()
        {
            SearchMustBeRunning = false;

            _ctsStopSearch?.Cancel();
            while (IsApiRunning || IsRssRunning || IsRss2Running || IsOofSRunning || _endNowFinder.IsEndNowRunning)
            {
                await Task.Delay(500).ConfigureAwait(false);
            }

            Running = false;
            _ctsStopSearch = new CancellationTokenSource();
            _cancellationToken = _ctsStopSearch.Token;
        }

        public async Task WaitForInitialSearch2()
        {
            while (true)
            {
                // Check if dual-phase loading is enabled and both phases are complete
                if (ConnectionConfig.LoadLowPriceFirst)
                {
                    if (!IsPriceFirstPhaseComplete || !IsRegularPhaseComplete)
                    {
                        await Task.Delay(1000, _cancellationToken).ContinueWith(_ => { }).ConfigureAwait(false);
                        continue;
                    }
                }
                else
                {
                    // For single-phase loading, check if regular phase is complete
                    if (!IsRegularPhaseComplete)
                    {
                        await Task.Delay(1000, _cancellationToken).ContinueWith(_ => { }).ConfigureAwait(false);
                        continue;
                    }
                }

                // Original completion logic
                if (UserSettings.MaxInitialRows - 10 > Stat.TotalItemsProcessed)
                {
                    if (Stat.ItemsFoundCounter - 15 > Stat.TotalItemsProcessed)
                    {
                        if (ProgramState.TotalRunningStopwatch.Elapsed.TotalMinutes < 5)
                        {
                            await Task.Delay(1000, _cancellationToken).ContinueWith(_ => { }).ConfigureAwait(false);

                            continue;
                        }
                        else
                        {
                        }
                    }
                    else
                    {
                    }
                }
                else
                {
                }

                if (_cancellationToken.IsCancellationRequested)
                {
                    return;
                }

                await Task.Delay(5 * 1000, _cancellationToken).ContinueWith(_ => { }).ConfigureAwait(false);

                return;
            }
        }

        internal static List<string> AddItemsToStorage(List<string> itemIDs)
        {
            var addedItems = new List<string>();
            foreach (var itemID in itemIDs)
            {
                if (Old.Keys.Contains(itemID))
                    continue;

                var globalSearch = new HashSet<string>
                {
                    "_!"
                };
                if (!Old.TryAdd(itemID, globalSearch))
                    continue;

                addedItems.Add(itemID);
                Interlocked.Increment(ref Stat.ItemsFoundCounter);
            }

            return addedItems;
        }

        internal static List<string> AddItemsToStorage(Keyword2Find kwFind, List<string> itemIDs)
        {
            var addedItems = new List<string>();

            foreach (var itemID in itemIDs)
            {
                if (Old.TryGetValue(itemID, out var itemState))
                {
                    if (itemState.Contains("_!"))
                    {
                        //Interlocked.Increment(ref Stat.ItemsFoundCounter);
                        //Increment when subsearch matches.
                    }
                    else
                    {
                        if (!itemState.Add(kwFind.Alias))
                            continue;
                        addedItems.Add(itemID);
                    }
                }
                else
                {
                    var subSearch = new HashSet<string>
                    {
                        kwFind.Alias
                    };
                    if (!Old.TryAdd(itemID, subSearch))
                        continue;
                    addedItems.Add(itemID);
                }
            }

            return addedItems;
        }
    }
}
