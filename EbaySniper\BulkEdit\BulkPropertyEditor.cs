using System;
using System.Collections.Generic;
using System.Linq;
using uBuyFirst.Search;

namespace uBuyFirst.BulkEdit
{
    /// <summary>
    /// Service class for performing bulk property operations on search terms within folder hierarchies
    /// </summary>
    public class BulkPropertyEditor
    {
        /// <summary>
        /// Protected fields that cannot be bulk edited
        /// </summary>
        private static readonly HashSet<string> ProtectedFields = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            "KeywordEnabled", "Enabled", "Alias", "Kws", "Keywords",
            "PurchasedQuantity", "JobId", "RequiredQuantity"
        };

        /// <summary>
        /// Gets all keywords from a folder and its recursive subfolders
        /// </summary>
        /// <param name="folder">The folder to traverse</param>
        /// <returns>List of all keywords in the folder hierarchy</returns>
        public List<Keyword2Find> GetAllKeywordsRecursive(KeywordFolder folder)
        {
            if (folder == null)
                throw new ArgumentNullException(nameof(folder));

            return folder.GetAllKeywords();
        }

        /// <summary>
        /// Validates if a property can be bulk edited
        /// </summary>
        /// <param name="propertyName">Name of the property to validate</param>
        /// <returns>True if the property can be bulk edited, false otherwise</returns>
        public bool IsPropertyBulkEditable(string propertyName)
        {
            return !ProtectedFields.Contains(propertyName);
        }

        /// <summary>
        /// Gets a list of all protected field names
        /// </summary>
        /// <returns>Collection of protected field names</returns>
        public IEnumerable<string> GetProtectedFields()
        {
            return ProtectedFields.AsEnumerable();
        }

        /// <summary>
        /// Applies bulk property changes to a collection of keywords
        /// </summary>
        /// <param name="keywords">Keywords to modify</param>
        /// <param name="propertyChanges">Dictionary of property names and their new values</param>
        /// <returns>Result of the bulk operation</returns>
        public BulkEditResult ApplyBulkChanges(List<Keyword2Find> keywords, Dictionary<string, object> propertyChanges)
        {
            if (keywords == null)
                throw new ArgumentNullException(nameof(keywords));

            if (propertyChanges == null)
                throw new ArgumentNullException(nameof(propertyChanges));

            var result = new BulkEditResult();
            var errors = new List<string>();

            // Validate all properties are bulk-editable
            foreach (var propertyName in propertyChanges.Keys)
            {
                if (!IsPropertyBulkEditable(propertyName))
                {
                    errors.Add($"Property '{propertyName}' is protected and cannot be bulk edited");
                }
            }

            if (errors.Any())
            {
                result.Success = false;
                result.Errors = errors;
                return result;
            }

            // Create backup for rollback capability
            var backup = CreateBackup(keywords);

            // Apply changes to each keyword
            var processedCount = 0;
            var failedKeywords = new List<string>();

            foreach (var keyword in keywords)
            {
                try
                {
                    ApplyChangesToKeyword(keyword, propertyChanges);
                    processedCount++;
                }
                catch (Exception ex)
                {
                    failedKeywords.Add($"Keyword '{keyword.Alias}': {ex.Message}");

                    // If we have failures, consider rolling back
                    if (failedKeywords.Count > keywords.Count * 0.1) // More than 10% failure rate
                    {
                        try
                        {
                            RestoreFromBackup(keywords, backup);
                            result.Success = false;
                            result.ProcessedCount = 0;
                            result.TotalCount = keywords.Count;
                            result.Errors = new List<string> { "Bulk operation failed and was rolled back due to high error rate." };
                            result.Errors.AddRange(failedKeywords);
                            return result;
                        }
                        catch (Exception rollbackEx)
                        {
                            failedKeywords.Add($"Rollback failed: {rollbackEx.Message}");
                        }
                    }
                }
            }

            result.Success = failedKeywords.Count == 0;
            result.ProcessedCount = processedCount;
            result.TotalCount = keywords.Count;
            result.Errors = failedKeywords;

            return result;
        }

        /// <summary>
        /// Applies property changes to a single keyword
        /// </summary>
        /// <param name="keyword">The keyword to modify</param>
        /// <param name="propertyChanges">Dictionary of property names and their new values</param>
        private void ApplyChangesToKeyword(Keyword2Find keyword, Dictionary<string, object> propertyChanges)
        {
            foreach (var change in propertyChanges)
            {
                var propertyName = change.Key;
                var newValue = change.Value;

                // Apply the property change based on property name
                switch (propertyName)
                {
                    case "SearchInDescription":
                        keyword.SearchInDescription = Convert.ToBoolean(newValue);
                        break;
                    case "Threads":
                        keyword.Threads = Convert.ToInt32(newValue);
                        break;
                    case "PriceMin":
                        keyword.PriceMin = Convert.ToDouble(newValue);
                        break;
                    case "PriceMax":
                        keyword.PriceMax = Convert.ToDouble(newValue);
                        break;
                    case "Zip":
                        keyword.Zip = newValue?.ToString() ?? "";
                        break;
                    case "LocatedIn":
                        keyword.LocatedIn = newValue?.ToString() ?? "";
                        break;
                    case "AvailableTo":
                        keyword.AvailableTo = newValue?.ToString() ?? "";
                        break;
                    case "SellerType":
                        keyword.SellerType = newValue?.ToString() ?? "";
                        break;
                    case "EbaySiteName":
                        keyword.EbaySiteName = newValue?.ToString() ?? "";
                        // Also set the EBaySite object for consistency
                        keyword.EBaySite = Intl.CountryProvider.GetEbaySite(newValue?.ToString() ?? "");
                        break;
                    case "ViewName":
                        keyword.ViewName = newValue?.ToString() ?? "";
                        break;
                    case "Frequency":
                        if (newValue is TimeSpan timeSpan)
                            keyword.Frequency = timeSpan;
                        break;
                    case "ListingType":
                        if (newValue is ListingType[] listingTypes)
                            keyword.ListingType = listingTypes;
                        break;
                    case "Condition":
                        if (newValue is string[] conditions)
                            keyword.Condition = conditions;
                        break;
                    case "Sellers":
                        if (newValue is string[] sellers)
                            keyword.Sellers = sellers;
                        break;
                    case "Categories4Api":
                        keyword.Categories4Api = newValue?.ToString() ?? "";
                        break;
                    default:
                        throw new ArgumentException($"Unknown property: {propertyName}");
                }
            }
        }

        /// <summary>
        /// Validates property values before applying changes
        /// </summary>
        /// <param name="propertyChanges">Dictionary of property names and their new values</param>
        /// <returns>List of validation errors, empty if all values are valid</returns>
        public List<string> ValidatePropertyValues(Dictionary<string, object> propertyChanges)
        {
            var errors = new List<string>();

            foreach (var change in propertyChanges)
            {
                var propertyName = change.Key;
                var value = change.Value;

                switch (propertyName)
                {
                    case "Threads":
                        if (!int.TryParse(value?.ToString(), out var threads) || threads < 1 || threads > 10)
                            errors.Add("Threads must be between 1 and 10");
                        break;
                    case "PriceMin":
                        if (!double.TryParse(value?.ToString(), out var priceMin) || priceMin < 0)
                            errors.Add("Price Min must be a positive number");
                        break;
                    case "PriceMax":
                        if (!double.TryParse(value?.ToString(), out var priceMax) || priceMax < 0)
                            errors.Add("Price Max must be a positive number");
                        break;
                    case "SearchInDescription":
                        if (!(value is bool))
                            errors.Add("Search In Description must be true or false");
                        break;
                    case "Zip":
                        var zipValue = value?.ToString();
                        if (!string.IsNullOrEmpty(zipValue) && zipValue.Length > 10)
                            errors.Add("ZIP code must be 10 characters or less");
                        break;
                    case "LocatedIn":
                    case "AvailableTo":
                        var locationValue = value?.ToString();
                        if (!string.IsNullOrEmpty(locationValue) && locationValue.Length > 50)
                            errors.Add($"{propertyName} must be 50 characters or less");
                        break;
                    case "ViewName":
                        var viewName = value?.ToString();
                        if (string.IsNullOrWhiteSpace(viewName))
                            errors.Add("View Name cannot be empty");
                        else if (viewName.Length > 100)
                            errors.Add("View Name must be 100 characters or less");
                        break;
                }
            }

            // Cross-field validation
            if (propertyChanges.ContainsKey("PriceMin") && propertyChanges.ContainsKey("PriceMax"))
            {
                if (double.TryParse(propertyChanges["PriceMin"]?.ToString(), out var min) &&
                    double.TryParse(propertyChanges["PriceMax"]?.ToString(), out var max))
                {
                    if (min > max)
                        errors.Add("Price Min cannot be greater than Price Max");
                }
            }

            return errors;
        }

        /// <summary>
        /// Creates a backup of keyword states before applying changes (for rollback)
        /// </summary>
        /// <param name="keywords">Keywords to backup</param>
        /// <returns>Dictionary containing backup data</returns>
        public Dictionary<string, Dictionary<string, object>> CreateBackup(List<Keyword2Find> keywords)
        {
            var backup = new Dictionary<string, Dictionary<string, object>>();

            foreach (var keyword in keywords)
            {
                var keywordBackup = new Dictionary<string, object>
                {
                    ["SearchInDescription"] = keyword.SearchInDescription,
                    ["Threads"] = keyword.Threads,
                    ["PriceMin"] = keyword.PriceMin,
                    ["PriceMax"] = keyword.PriceMax,
                    ["Zip"] = keyword.Zip,
                    ["LocatedIn"] = keyword.LocatedIn,
                    ["AvailableTo"] = keyword.AvailableTo,
                    ["SellerType"] = keyword.SellerType,
                    ["EbaySiteName"] = keyword.EbaySiteName,
                    ["ViewName"] = keyword.ViewName,
                    ["Frequency"] = keyword.Frequency,
                    ["ListingType"] = keyword.ListingType?.ToArray(),
                    ["Condition"] = keyword.Condition?.ToArray(),
                    ["Sellers"] = keyword.Sellers?.ToArray()
                };

                backup[keyword.Id] = keywordBackup;
            }

            return backup;
        }

        /// <summary>
        /// Restores keywords from backup data (rollback functionality)
        /// </summary>
        /// <param name="keywords">Keywords to restore</param>
        /// <param name="backup">Backup data to restore from</param>
        public void RestoreFromBackup(List<Keyword2Find> keywords, Dictionary<string, Dictionary<string, object>> backup)
        {
            foreach (var keyword in keywords)
            {
                if (backup.ContainsKey(keyword.Id))
                {
                    var keywordBackup = backup[keyword.Id];
                    ApplyChangesToKeyword(keyword, keywordBackup);
                }
            }
        }
    }
}
